{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"target": "ESNext", "lib": ["DOM", "ESNext", "WebWorker"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "types": ["vite/client", "@webgpu/types"], "allowJs": true, "strict": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules"]}