/***********************  简体中文注释  ************************
 * Tailwind CSS 基础配置
 * - content 指向所有可能包含类名的文件（Svelte/Vue/TS/JS）
 * - 暗色模式使用 class 切换（兼容原项目的 useDark 逻辑）
 *************************************************************/

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/**/*.{svelte,ts,js}',
    './index.html',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
};

