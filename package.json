{"name": "svelte-projet-1", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "dependencies": {"@huggingface/transformers": "^3.5.1", "@xsai-transformers/shared": "^0.0.6", "gpuu": "^1.0.3"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@webgpu/types": "^0.1.60", "autoprefixer": "^10.4.20", "postcss": "^8.4.41", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.13", "typescript": "^5.0.0", "vite": "^7.0.4"}}