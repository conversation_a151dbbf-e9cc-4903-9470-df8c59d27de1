<h1 align="center">SmolVLM Realtime WebGPU (Vue)</h1>

<p align="center">
  [<a href="https://smolvlm-realtime-webgpu-vue.netlify.app/">Try it</a>]
</p>

> Heavily inspired by [ngxson/smolvlm-realtime-webcam: Real-time webcam demo with SmolVLM and llama.cpp server](https://github.com/ngxson/smolvlm-realtime-webcam?tab=readme-ov-file)

# SmolVLM Realtime WebGPU

## Getting Started

Follow the steps below to set up and run the application.

### 1. Clone the Repository

Clone the examples repository from GitHub:

```sh
git clone https://github.com/proj-airi/webai-examples.git
```

### 2. Navigate to the Project Directory

Change your working directory to the `smolvlm-realtime-webgpu` folder:

```sh
cd apps/smolvlm-realtime-webgpu
```

### 3. Install Dependencies

Install the necessary dependencies using npm:

```sh
npm i
```

### 4. Run the Development Server

Start the development server:

```sh
npm run dev
```

The application should now be running locally. Open your browser and go to `http://localhost:5175` to see it in action.

## Acknowledgements

Great thanks to what Xenova have done.

> [Source code](https://huggingface.co/spaces/webml-community/smolvlm-realtime-webgpu/blob/main/index.html)
