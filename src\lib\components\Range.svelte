<script lang="ts">
  import { onMount } from 'svelte';
  export let min = 0;
  export let max = 100;
  export let step = 1;
  export let disabled = false;
  export let value = 0; // 简体中文：父组件以 bind:value 双向绑定

  let slider: HTMLInputElement;
  function handleInput(e: Event) {
    const target = e.target as HTMLInputElement;
    value = Number(target.value);
  }
  onMount(() => { /* 可扩展：设置 CSS 变量以模拟旧样式 */ });
</script>

<input
  bind:this={slider}
  type="range"
  {min}
  {max}
  {step}
  {disabled}
  value={value}
  on:input={handleInput}
  class="h-8 w-full appearance-none rounded bg-transparent"
/>

<style>
  /* 简体中文：基础范围输入样式（可逐步优化为更接近旧版视觉） */
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 8px;
    height: 2rem;
    background: #e1e1e1;
    border-radius: 999px;
  }
  input[type="range"]::-webkit-slider-runnable-track {
    height: 2rem;
    border-radius: 4px;
    background: linear-gradient(to right, #ef4444 0%, #ef4444 calc((var(--value, 0) - var(--min, 0))/(var(--max, 1) - var(--min, 0)) * 100%), #6b7280 0%);
  }
</style>

