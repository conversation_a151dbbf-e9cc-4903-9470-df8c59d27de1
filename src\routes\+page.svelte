<script lang="ts">
  // 简体中文：将摄像头采集 + 模型推理逻辑迁入 Svelte 页面
  import { onMount, onDestroy } from 'svelte'
  import type { LoadOptionProgressCallback, ProgressInfo, ProgressStatusInfo } from '@xsai-transformers/shared/types'

  import type { VLMWorker } from '$libs/vlm'
  import { createVLMWorker } from '$libs/vlm'
  import workerURL from '$libs/worker?worker&url'
  import Progress from '$lib/components/Progress.svelte'

  // 简体中文：GPU 支持检测（不依赖第三方）
  async function checkWebGPU() {
    try {
      const supported = typeof navigator !== 'undefined' && !!navigator.gpu
      if (!supported) return { supported: false }
      const adapter = await navigator.gpu?.requestAdapter()
      return { supported: !!adapter }
    } catch {
      return { supported: false }
    }
  }

  let videoEl: HTMLVideoElement
  let canvasEl: HTMLCanvasElement
  let stream: MediaStream | null = null

  let isWebGPUSupported = true
  let loaded = false
  let isProcessing = false
  let isModelLoading = false
  let isWebGPULoading = false

  let instructionText = 'In one sentence, what do you see?'
  let responseText = ''

  // 简体中文：性能控制
  let scale = 0.3
  let maxImageSize = 224
  let processingInterval = 2000

  // 简体中文：性能监控
  let fpsCounter = 0
  let lastFrameTime = 0
  let processingTime = 0

  // 简体中文：加载进度
  let loadingItems: ProgressInfo[] = []
  const loadingItemsSet = new Set<string>()
  let overallProgress = 0
  let overallTotal = 0

  let vlmWorker: VLMWorker

  function captureImage() {
    if (!stream || !videoEl?.videoWidth || !canvasEl) {
      console.warn('视频流未准备好')
      return null
    }
    const originalWidth = videoEl.videoWidth
    const originalHeight = videoEl.videoHeight
    const scaledWidth = Math.round(originalWidth * scale)
    const scaledHeight = Math.round(originalHeight * scale)

    const aspectRatio = originalWidth / originalHeight
    let finalWidth = scaledWidth
    let finalHeight = scaledHeight
    if (Math.max(scaledWidth, scaledHeight) > maxImageSize) {
      if (scaledWidth > scaledHeight) {
        finalWidth = maxImageSize
        finalHeight = Math.round(maxImageSize / aspectRatio)
      } else {
        finalHeight = maxImageSize
        finalWidth = Math.round(maxImageSize * aspectRatio)
      }
    }

    canvasEl.width = finalWidth
    canvasEl.height = finalHeight
    const ctx = canvasEl.getContext('2d', { willReadFrequently: true })
    if (!ctx) return null
    ctx.drawImage(videoEl, 0, 0, finalWidth, finalHeight)
    const frame = ctx.getImageData(0, 0, finalWidth, finalHeight)
    return { imageBuffer: frame.data, imageWidth: frame.width, imageHeight: frame.height, channels: 4 as 1 | 2 | 3 | 4 }
  }

  async function sendData() {
    if (!isProcessing) return
    const rawImg = captureImage()
    if (!rawImg) {
      responseText = 'Capture failed'
      return
    }
    try {
      const startTime = performance.now()
      const response = await vlmWorker?.process({
        instruction: instructionText,
        imageBuffer: rawImg.imageBuffer,
        imageWidth: rawImg.imageWidth,
        imageHeight: rawImg.imageHeight,
        channels: rawImg.channels,
      })
      const endTime = performance.now()
      processingTime = Math.round(endTime - startTime)
      if (lastFrameTime) {
        const frameTime = endTime - lastFrameTime
        fpsCounter = Math.round((1000 / frameTime) * 100) / 100
      }
      lastFrameTime = endTime
      responseText = response ?? ''
    } catch (e) {
      console.error(e)
      responseText = `Error: ${e instanceof Error ? e.message : 'Unknown error'}`
    }
  }

  let rafId: number | null = null
  let lastProcessTime = 0
  function processingLoop() {
    if (!isProcessing) {
      if (rafId !== null) cancelAnimationFrame(rafId)
      rafId = null
      return
    }
    const now = performance.now()
    if (now - lastProcessTime >= processingInterval) {
      lastProcessTime = now
      sendData().finally(() => {
        if (isProcessing) rafId = requestAnimationFrame(processingLoop)
      })
    } else {
      if (isProcessing) rafId = requestAnimationFrame(processingLoop)
    }
  }

  const onProgress: LoadOptionProgressCallback = (progress) => {
    if (progress.status === 'initiate') {
      if (loadingItemsSet.has(progress.file)) return
      loadingItemsSet.add(progress.file)
      loadingItems = [...loadingItems, progress]
      isModelLoading = true
    } else if (progress.status === 'progress') {
      const idx = loadingItems.findIndex((i) => (i as ProgressStatusInfo).file === progress.file)
      if (idx >= 0) loadingItems[idx] = progress
      else loadingItems = [...loadingItems, progress]
      let newTotal = 0, newLoaded = 0
      for (const item of loadingItems) {
        // @ts-expect-error 动态结构
        if ('total' in item && item.total) newTotal += item.total
        // @ts-expect-error 动态结构
        if ('loaded' in item && item.loaded) newLoaded += item.loaded
      }
      overallTotal = newTotal
      if (newTotal > 0) overallProgress = (newLoaded / newTotal) * 100
    } else if (progress.status === 'done' || progress.status === 'ready') {
      isModelLoading = false
      overallProgress = 100
    }
  }

  async function handleStart() {
    if (!stream) {
      responseText = 'Camera not available. Cannot start.'
      return
    }
    if (!loaded) {
      isWebGPULoading = true
      await vlmWorker?.load({ onProgress } as any)
      isWebGPULoading = false
      loaded = true
    }
    isProcessing = true
    responseText = '...'
    processingLoop()
  }

  function handleStop() {
    isProcessing = false
    if (rafId !== null) cancelAnimationFrame(rafId)
    rafId = null
    if (responseText === '...') responseText = ''
  }

  async function startStream() {
    try {
      stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false })
      if (videoEl) videoEl.srcObject = stream
    } catch (e) {
      console.warn('获取摄像头权限失败', e)
    }
  }

  onMount(async () => {
    vlmWorker = createVLMWorker({ baseURL: workerURL })
    const result = await checkWebGPU()
    isWebGPUSupported = result.supported
    await startStream()
  })
  onDestroy(() => {
    vlmWorker?.dispose()
    if (stream) stream.getTracks().forEach(t => t.stop())
    if (rafId) cancelAnimationFrame(rafId)
  })
</script>

<div class="relative min-h-dvh w-full p-4">
  <div class="relative h-[70vh] w-full overflow-hidden rounded-2xl shadow">
    <video bind:this={videoEl} autoplay muted playsinline class="h-full w-full object-cover"></video>
    <canvas bind:this={canvasEl} class="hidden"></canvas>

    <!-- 控制条 -->
    <div class="absolute left-4 top-4 z-10 flex items-center gap-3">
      <button
        class="rounded-full bg-green-700/70 px-4 py-2 text-white backdrop-blur hover:bg-green-700"
        on:click={handleStart}
        disabled={isProcessing}
      >{isWebGPULoading || isModelLoading ? 'Loading...' : 'Start'}</button>
      <button
        class="rounded-full bg-red-700/70 px-4 py-2 text-white backdrop-blur hover:bg-red-700 disabled:opacity-50"
        on:click={handleStop}
        disabled={!isProcessing}
      >Stop</button>
    </div>

    <!-- 进度条与推理结果 -->
    {#if isModelLoading}
      <div class="absolute bottom-20 left-1/2 z-10 w-1/2 -translate-x-1/2">
        <Progress percentage={Math.min(100, overallProgress)} />
      </div>
    {/if}

    {#if responseText}
      <div class="absolute bottom-4 left-1/2 z-10 max-w-[80%] -translate-x-1/2 rounded-xl bg-neutral-800/70 px-3 py-2 text-white backdrop-blur">
        {responseText}
      </div>
    {/if}

    <!-- 能力与权限提示 -->
    {#if !isWebGPUSupported}
      <div class="absolute inset-0 z-10 flex items-center justify-center bg-neutral-50/40">
        <div class="rounded-xl bg-white/80 p-4 text-neutral-800 shadow">Browser does not support WebGPU.</div>
      </div>
    {/if}
  </div>

  <!-- 参数控制（简化版） -->
  <div class="mt-4 grid grid-cols-[auto_1fr_auto] items-center gap-x-3 gap-y-2 text-sm">
    <div>Scale:</div>
    <input type="range" min={0.1} max={1.0} step={0.1} bind:value={scale} disabled={isProcessing} class="w-full" />
    <div class="text-right font-mono">{scale.toFixed(1)}</div>

    <div>Max Size:</div>
    <input type="range" min={128} max={512} step={32} bind:value={maxImageSize} disabled={isProcessing} class="w-full" />
    <div class="text-right font-mono">{maxImageSize}</div>

    <div>Interval:</div>
    <input type="range" min={500} max={5000} step={250} bind:value={processingInterval} class="w-full" />
    <div class="text-right font-mono">{(processingInterval / 1000).toFixed(1)}s</div>

    <div>Ask:</div>
    <input type="text" bind:value={instructionText} placeholder="What do you see?" class="w-full rounded-lg border px-2 py-1" />
    <div></div>
  </div>
</div>
